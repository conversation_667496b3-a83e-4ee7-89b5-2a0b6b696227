using UnityEngine;

public static class ColorUtils
{
    /// <summary>
    /// Calculates a basic contrasting 'on' color (black or white) for a given base color
    /// based on its perceived luminance. This uses WCAG 2.1 luminance calculation.
    /// </summary>
    /// <param name="baseColor">The input color (sRGB) to determine the 'on' color for.</param>
    /// <param name="luminanceThreshold">The threshold to determine if the base color is light or dark. Defaults to 0.5.</param>
    /// <returns>The calculated 'on' color (either Color.black or Color.white).</returns>
    public static Color GetOnColor(Color baseColor, float luminanceThreshold = 0.5f)
    {
        // 1. Get linear RGB components.
        // Unity's Color.linear property converts sRGB to linear automatically.
        Color linearBase = baseColor.linear;
        float rLinear = linearBase.r;
        float gLinear = linearBase.g;
        float bLinear = linearBase.b;

        // 2. Calculate Luminance (L) using WCAG coefficients.
        float luminance = 0.2126f * rLinear +
                          0.7152f * gLinear +
                          0.0722f * bLinear;

        // 3. Choose 'onColor' based on luminance against the threshold.
        return luminance > luminanceThreshold
            ? Color.black // Base color is light, use black for contrast.
            : Color.white; // Base color is dark, use white for contrast.
    }
}