using UnityEngine;

namespace Soup
{
    public class ColorTheme : ScriptableObject, IPlayModeStateChangeReceiver
    {
        public ColorStore PrimaryColor;
        public ColorStore SecondaryColor;
        public ColorStore TertiaryColor;
        public ColorStore ErrorColor;
        public ColorStore BackgroundColor;
        
        public void OnWillEnterPlayMode()
        {
            PrimaryColor.OnWillEnterPlayMode();
            SecondaryColor.OnWillEnterPlayMode();
            TertiaryColor.OnWillEnterPlayMode();
            ErrorColor.OnWillEnterPlayMode();
            BackgroundColor.OnWillEnterPlayMode();
        }

        public void OnExitingPlayMode()
        {
            PrimaryColor.OnExitingPlayMode();
            SecondaryColor.OnExitingPlayMode();
            TertiaryColor.OnExitingPlayMode();
            ErrorColor.OnExitingPlayMode();
            BackgroundColor.OnExitingPlayMode();
        }
    }
}