# Bindings

Bindings are components that create reactive connections between [Value Stores](value-stores.md) and Unity UI elements. They automatically update UI properties when the underlying data changes, enabling a data-driven approach to UI development.

## Overview

Bindings eliminate the need to manually update UI elements when data changes. Instead of writing code to sync UI with your data, you simply connect a Value Store to a UI component through a binding, and the UI will automatically reflect any changes to the data.

All binding components:
- Execute in Edit Mode for immediate visual feedback
- Automatically subscribe to Value Store changes
- Handle cleanup when disabled or destroyed
- Support real-time updates in the Unity Inspector

## Available Bindings

Soup provides several built-in binding components for common UI scenarios:

### ImageColorBinding

Binds a [ColorStore](value-stores.md#colorstore) to any UI Graphic component (Image, RawImage, Text, etc.).

**Requirements:**
- Target GameObject must have a `Graphic` component

**Usage:**
1. Add the `ImageColorBinding` component to a GameObject with a Graphic component
2. Assign a ColorStore asset to the `Color Store` field
3. The Graphic's color will automatically update when the ColorStore value changes

**Menu Path:** `Add Component > Soup > Bindings > Image - Color Binding`

```csharp
// Example: Changing color through code will automatically update the UI
[SerializeField] private ColorStore _buttonColor;

void ChangeButtonColor()
{
    _buttonColor.Value = Color.red; // UI automatically updates
}
```

### TMP_TextColorBinding

Binds a [ColorStore](value-stores.md#colorstore) to a TextMeshPro text component's color.

**Requirements:**
- Target GameObject must have a `TMP_Text` component

**Usage:**
1. Add the `TMP_TextColorBinding` component to a GameObject with a TMP_Text component
2. Assign a ColorStore asset to the `Color Store` field
3. The text color will automatically update when the ColorStore value changes

**Menu Path:** `Add Component > Soup > Bindings > TextMeshPro - Text Color Binding`

### TMP_TextFontSizeBinding

Binds a [FloatStore](value-stores.md#floatstore) to a TextMeshPro text component's font size.

**Requirements:**
- Target GameObject must have a `TMP_Text` component

**Usage:**
1. Add the `TMP_TextFontSizeBinding` component to a GameObject with a TMP_Text component
2. Assign a FloatStore asset to the `Font Size Store` field
3. The text font size will automatically update when the FloatStore value changes

**Menu Path:** `Add Component > Soup > Bindings > TextMeshPro - Font Size Binding`

```csharp
// Example: Dynamic font size adjustment
[SerializeField] private FloatStore _fontSize;

void IncreaseFontSize()
{
    _fontSize.Value += 2f; // Text size automatically updates
}
```

## Common Use Cases

### Theme Systems

Bindings are perfect for implementing theme systems where colors and sizes need to change globally:

```csharp
// Create ColorStore assets for your theme
public ColorStore PrimaryColor;
public ColorStore SecondaryColor;
public ColorStore TextColor;

// Bind UI elements to these stores
// When theme changes, all bound UI elements update automatically
```

### Accessibility Features

Use bindings to implement accessibility features like font size scaling:

```csharp
// Single FloatStore controls font size across the entire UI
[SerializeField] private FloatStore _globalFontSizeMultiplier;

// All text elements bound to this store will scale together
```

### Dynamic UI States

Create responsive UI that reacts to game state changes:

```csharp
// Health bar color changes based on player health
[SerializeField] private ColorStore _healthBarColor;

void UpdateHealthColor(float healthPercentage)
{
    _healthBarColor.Value = Color.Lerp(Color.red, Color.green, healthPercentage);
    // All UI elements bound to this store update automatically
}
```

## Best Practices

1. **Create Meaningful Asset Names**: Name your Value Store assets descriptively (e.g., "Primary Button Color", "Header Font Size")

2. **Group Related Stores**: Consider using folders to organize related Value Stores by feature or theme

3. **Use ColorTheme**: For complex color schemes, consider using Soup's [ColorTheme](value-stores.md#color-themes) system

4. **Test in Edit Mode**: Bindings work in Edit Mode, so you can test different values without entering Play Mode

5. **Avoid Circular Dependencies**: Don't create situations where bindings might cause infinite update loops

## Performance Considerations

- Bindings use Unity's event system and are optimized for performance
- Updates only occur when the underlying Value Store actually changes
- Bindings automatically clean up their subscriptions when disabled or destroyed
- Edit Mode execution allows for immediate visual feedback without runtime overhead during development

## Integration with Other Systems

Bindings work seamlessly with other Soup features:
- **Value Stores**: The foundation that provides the reactive data
- **Events**: Can trigger Value Store changes that propagate through bindings
- **Collection Stores**: Can be used to manage lists of UI elements with bindings
