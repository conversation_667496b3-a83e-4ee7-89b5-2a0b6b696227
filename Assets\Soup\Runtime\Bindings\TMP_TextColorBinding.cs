using TMPro;
using UnityEngine;

namespace Soup
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(TMP_Text))]
    [AddComponentMenu(Constants.BINDINGS_MENU_PATH + "TextMeshPro - Text Color Binding")]
    public class TMP_TextColorBinding : MonoBehaviour
    {
        [SerializeField]
        private ColorStore _colorStore;

        private TMP_Text _text;
        private EventSubscription _subscription;

        private void Awake()
        {
            _text = GetComponent<TMP_Text>();
        }

        private void OnEnable()
        {
            if (!_colorStore) return;

            _text.color = _colorStore.Value;
            _subscription = _colorStore.AddListener(OnColorChanged);
        }

        private void OnDisable()
        {
            _subscription?.Dispose();
        }

        private void OnColorChanged(Color color)
        {
            _text.color = color;
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (!_colorStore) return;

            if (!_text)
                _text = GetComponent<TMP_Text>();

            _text.color = _colorStore.Value;
        }
#endif
    }
}