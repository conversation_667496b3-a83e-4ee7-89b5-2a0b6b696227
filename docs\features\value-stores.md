# Value Stores

Value Stores are ScriptableObject-based containers that hold a single value of a specific type. They serve as a centralized, persistent data storage solution that can be easily referenced across your Unity project.

Soup provides several built-in Value Store types for common Unity data types:
- `IntStore` - for integer values
- `FloatStore` - for floating point numbers
- `StringStore` - for text data
- `Vector2Store` - for 2D coordinates
- `Vector3Store` - for 3D positions, rotations, or scales
- `QuaternionStore` - for rotations

You can also create custom Value Stores for your own data types using the [Generator tool](../tools/generator.md).

## Play Mode Behavior

Value Stores automatically track their initial values when entering Play Mode and can reset to those values when exiting Play Mode. This behavior is controlled by the `_resetAfterPlayModeExit` flag, which is enabled by default.

## Referencing Value Stores

To use a Value Store in your MonoBehaviour, simply add a serialized field of the appropriate type:

```csharp
public class MyBehaviour : MonoBehaviour
{
    [SerializeField] private IntStore _playerScore;
    [SerializeField] private Vector3Store _spawnPosition;
    [SerializeField] private StringStore _playerName;
    
    // Custom type example
    [SerializeField] private PlayerStateStore _playerState;
}
```

Then drag and drop the Value Store asset into the field in the Inspector. This creates a reference to the shared data container that can be accessed from anywhere in your project.

## Getting the value

Accessing the value stored in a Value Store is straightforward using the `Value` property:

```csharp
// Reading values
int score = _playerScore.Value;
Vector3 position = _spawnPosition.Value;
string name = _playerName.Value;

// For custom types, you can access their properties directly
int playerLevel = _playerState.Value.Level;
```

You can also modify the value using the same property:

```csharp
// Setting values
_playerScore.Value = 100;
_spawnPosition.Value = new Vector3(0, 1, 0);
_playerName.Value = "Player One";

// For custom types, you need to create a new instance
_playerState.Value = new PlayerState { Level = 5, Position = transform.position };
```

?> When you modify a Value Store, all listeners will be automatically notified of the change.

## Events

Value Stores provide a powerful event system that allows you to react to value changes. There are two main ways to listen for changes:

### Basic Listening

The simplest way to listen for changes is to add a listener that will be called whenever the value changes:

```csharp
EventSubscription subscription = myValueStore.AddListener(newValue => {
    Debug.Log($"Value changed to: {newValue}");
});

// Later, when you no longer need the listener
subscription.Dispose();
```

### Selective Listening

Sometimes you only want to be notified when a specific property of a complex value changes. Value Stores support this through selective listeners:

```csharp
// Only listen for changes to the Latitude property
EventSubscription subscription = gpsCoordinateStore.AddListener(
    value => value.Latitude,  // Select the property to watch
    newLatitude => {          // Only called when Latitude changes
        Debug.Log($"Latitude changed to: {newLatitude}");
    }
);
```

### Removing Listeners

All listeners return an `EventSubscription` that you can use to unsubscribe from the event later:

```csharp
subscription.Dispose();
```

You can also remove all listeners at once:

```csharp
myValueStore.RemoveAllListeners();
```

## Caveats

### Value Types vs Reference Types

When working with Value Stores containing reference types (like custom classes), be aware that modifying properties of the referenced object directly won't trigger listeners. For example:

```csharp
// This will NOT trigger listeners
_playerStateStore.Value.Level = 10;

// Instead, create a new instance or clone the existing one
var newState = _playerStateStore.Value;
newState.Level = 10;
_playerStateStore.Value = newState;
```

### Equality Comparison

Value Stores use the `Equals` method to determine if a value has changed. For custom types, ensure you've properly implemented the `IEquatable<T>` interface to avoid unnecessary updates or missed change notifications.

### Performance Considerations

While Value Stores are convenient, be mindful when using them for frequently changing values in performance-critical code. Each value change triggers notifications to all listeners, which can impact performance if overused.

## Best Practices

### Use C# Records for Immutability

C# records (available in C# 9.0+) are the ideal choice for Value Store types as they solve the issues with reference types:

```csharp
// Define your data as a record
public record PlayerState(int Health, int Level, Vector3 Position);

// Create a Value Store for it
[CreateAssetMenu(menuName = "Soup/Values/Player State")]
public class PlayerStateStore : ValueStore<PlayerState> { }

// Usage with non-destructive updates
void TakeDamage(int damage) {
    // Creates a new instance with just the Health property changed
    _playerStateStore.Value = _playerStateStore.Value with { Health = _playerStateStore.Value.Health - damage };
}
```

Benefits of Records with Value Stores:
- Since records are immutable, you must create a new instance to change values, ensuring listeners are always notified
- Value-based equality (comparing all properties)
- Copy construction with the `with` expression
