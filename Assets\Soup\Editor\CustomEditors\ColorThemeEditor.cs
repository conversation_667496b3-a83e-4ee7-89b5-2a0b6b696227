using UnityEditor;
using UnityEngine;

namespace Soup
{
    [CustomEditor(typeof(ColorTheme))]
    public class ColorThemeEditor : Editor
    {
        private void OnEnable()
        {
            var theme = (ColorTheme)target;

            theme.PrimaryColor ??= ColorThemeCreator.CreateSubAsset(theme, "Primary Color");
            theme.SecondaryColor ??= ColorThemeCreator.CreateSubAsset(theme, "Secondary Color");
            theme.TertiaryColor ??= ColorThemeCreator.CreateSubAsset(theme, "Tertiary Color");
            theme.ErrorColor ??= ColorThemeCreator.CreateSubAsset(theme, "Error Color");
            theme.BackgroundColor ??= ColorThemeCreator.CreateSubAsset(theme, "Background Color");
        }

        public override void OnInspectorGUI()
        {
            GUI.enabled = false;
            base.OnInspectorGUI();
            GUI.enabled = true;
        }
    }
}