using UnityEditor;
using UnityEngine;
using static UnityEngine.ScriptableObject;

namespace Soup
{
    public static class ColorThemeCreator
    {
        [MenuItem("Assets/Create/Soup/Color Theme/Color Theme")]
        public static void CreateColorTheme()
        {
            ColorTheme theme = CreateInstance<ColorTheme>();
            AssetDatabase.CreateAsset(theme, "Assets/Color Theme.asset");
            
            theme.PrimaryColor = CreateSubAsset(theme, "Primary Color");
            theme.SecondaryColor = CreateSubAsset(theme, "Secondary Color");
            theme.TertiaryColor = CreateSubAsset(theme, "Tertiary Color");

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        public static ColorStore CreateSubAsset(ColorTheme theme, string colorName)
        {
            var color = CreateInstance<ColorStore>();
            color.name = colorName;
            color.Value = Color.white;
            AssetDatabase.AddObjectToAsset(color, theme);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            return color;
        }
    }
}